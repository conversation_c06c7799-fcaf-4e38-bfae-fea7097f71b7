#!/usr/bin/env python3
"""
Amazon.in Laptop Search Test
Smart Browser Automation Agent test for e-commerce automation
"""

import asyncio
import json
from smart_browser_agent import SmartAutomationAgent, UserGoal


async def test_amazon_laptop_search():
    """Test Amazon.in laptop search automation"""
    print("="*60)
    print("AMAZON.IN LAPTOP SEARCH AUTOMATION TEST")
    print("="*60)
    
    # Define the Amazon laptop search goal
    goal = UserGoal(
        goal="Navigate to Amazon.in and search for laptop",
        website_url="https://www.amazon.in/",
        context="E-commerce automation",
        max_steps=15,
        timeout=300
    )
    
    print(f"🎯 Goal: {goal.goal}")
    print(f"🌐 Website: {goal.website_url}")
    print(f"📝 Context: {goal.context}")
    print(f"⏱️  Max Steps: {goal.max_steps}")
    print(f"⏰ Timeout: {goal.timeout} seconds")
    print("\n" + "="*60)
    
    # Initialize the Smart Automation Agent
    agent = SmartAutomationAgent(
        headless=False,  # Set to True for headless execution
        model_name="gemini-2.0-flash"
    )
    
    try:
        print("🚀 Starting Amazon laptop search automation...")
        print("📋 The agent will:")
        print("   1. Navigate to Amazon.in")
        print("   2. Find the search box")
        print("   3. Enter 'laptop' as search term")
        print("   4. Execute the search")
        print("   5. Capture results and screenshots")
        print("\n⏳ Executing automation...\n")
        
        # Execute the goal
        result = await agent.execute_goal(goal)
        
        # Display results
        print("\n" + "="*60)
        print("EXECUTION RESULTS")
        print("="*60)
        
        print(f"✅ Success: {result.success}")
        print(f"📊 Steps Executed: {result.steps_executed}")
        print(f"⏱️  Total Time: {result.total_time:.2f} seconds")
        print(f"🌐 Final URL: {result.final_url}")
        
        if result.extracted_data:
            print(f"\n📄 Extracted Data:")
            for key, value in result.extracted_data.items():
                print(f"   {key}: {value}")
        
        if result.error_summary:
            print(f"\n❌ Errors: {result.error_summary}")
        
        print(f"\n📸 Screenshots: {len(result.screenshots)} captured")
        if result.screenshots:
            print("   Screenshot files:")
            for screenshot in result.screenshots:
                print(f"   - {screenshot}")
        
        print(f"\n📝 Execution Log: {len(result.execution_log)} entries")
        
        # Display detailed execution log
        print("\n" + "="*60)
        print("DETAILED EXECUTION LOG")
        print("="*60)
        for i, log_entry in enumerate(result.execution_log, 1):
            print(f"{i:2d}. {log_entry}")
        
        # Success/Failure analysis
        print("\n" + "="*60)
        print("ANALYSIS")
        print("="*60)
        
        if result.success:
            print("🎉 SUCCESS: Amazon laptop search automation completed successfully!")
            print("✅ The agent was able to:")
            print("   - Navigate to Amazon.in")
            print("   - Locate and interact with the search functionality")
            print("   - Execute the laptop search")
            
            if "amazon.in" in result.final_url.lower() and "laptop" in result.final_url.lower():
                print("   - Successfully reached laptop search results page")
            else:
                print("   - Reached Amazon but may not have completed search")
                
        else:
            print("❌ PARTIAL SUCCESS: Some issues encountered during automation")
            print("🔍 Common issues and solutions:")
            print("   - Cookie/GDPR popups: Agent should handle these automatically")
            print("   - Search box location: Agent adapts to different page layouts")
            print("   - Network delays: Increase timeout if needed")
            print("   - Captcha challenges: May require manual intervention")
        
        print(f"\n📈 Efficiency: {result.steps_executed}/{goal.max_steps} steps used")
        if result.steps_executed > 0:
            print(f"⚡ Speed: {result.total_time/result.steps_executed:.1f} seconds per step")
        else:
            print("⚡ Speed: No steps executed")
        
        return result
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None


async def test_amazon_advanced_search():
    """Test more advanced Amazon search with filters"""
    print("\n" + "="*60)
    print("AMAZON.IN ADVANCED LAPTOP SEARCH TEST")
    print("="*60)
    
    # Define a more complex goal
    goal = UserGoal(
        goal="Navigate to Amazon.in, search for 'gaming laptop', and filter results by price range under ₹50,000",
        website_url="https://www.amazon.in/",
        context="Advanced e-commerce automation with filtering",
        max_steps=20,
        timeout=400
    )
    
    print(f"🎯 Advanced Goal: {goal.goal}")
    print("📋 This test will attempt more complex interactions:")
    print("   1. Navigate to Amazon.in")
    print("   2. Search for 'gaming laptop'")
    print("   3. Apply price filter (under ₹50,000)")
    print("   4. Extract filtered results")
    
    agent = SmartAutomationAgent(headless=False, model_name="gemini-2.0-flash")
    
    try:
        result = await agent.execute_goal(goal)
        
        print(f"\n✅ Advanced Test Success: {result.success}")
        print(f"📊 Steps Used: {result.steps_executed}/{goal.max_steps}")
        print(f"⏱️  Time Taken: {result.total_time:.2f} seconds")
        
        return result
        
    except Exception as e:
        print(f"❌ Advanced test failed: {e}")
        return None


async def run_amazon_tests():
    """Run all Amazon-related tests"""
    print("🛒 AMAZON.IN AUTOMATION TEST SUITE")
    print("="*60)
    
    tests = [
        ("Basic Laptop Search", test_amazon_laptop_search),
        ("Advanced Gaming Laptop Search", test_amazon_advanced_search),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running: {test_name}")
            result = await test_func()
            
            if result:
                results.append((test_name, result.success, result.steps_executed, result.total_time))
            else:
                results.append((test_name, False, 0, 0))
            
            # Wait between tests
            print("\n⏳ Waiting 3 seconds before next test...")
            await asyncio.sleep(3)
            
        except Exception as e:
            print(f"❌ Test {test_name} failed with error: {e}")
            results.append((test_name, False, 0, 0))
    
    # Final summary
    print("\n" + "="*80)
    print("AMAZON AUTOMATION TEST SUMMARY")
    print("="*80)
    
    print(f"{'Test Name':<35} | {'Status':<6} | {'Steps':<5} | {'Time':<8}")
    print("-" * 80)
    
    total_tests = len(results)
    passed_tests = 0
    
    for test_name, success, steps, time_taken in results:
        status = "PASS" if success else "FAIL"
        if success:
            passed_tests += 1
        print(f"{test_name:<35} | {status:<6} | {steps:<5} | {time_taken:<8.1f}s")
    
    print("-" * 80)
    print(f"Total: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL AMAZON TESTS PASSED!")
        print("🛒 The Smart Browser Agent successfully automated Amazon.in interactions!")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) had issues.")
        print("💡 This is normal for complex e-commerce sites with dynamic content.")


if __name__ == "__main__":
    # You can run specific tests or all tests
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--basic":
            # Run only basic test
            asyncio.run(test_amazon_laptop_search())
        elif sys.argv[1] == "--advanced":
            # Run only advanced test
            asyncio.run(test_amazon_advanced_search())
        else:
            print("Usage: python test_amazon_search.py [--basic|--advanced]")
    else:
        # Run all tests
        asyncio.run(run_amazon_tests())
