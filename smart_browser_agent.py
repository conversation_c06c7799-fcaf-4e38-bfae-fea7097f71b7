#!/usr/bin/env python3
"""
Smart Browser Automation Agent
LLM-driven browser automation with Planner-Executor architecture

Components:
- Planner: LLM-powered goal decomposition and selector determination
- Executor: Browser automation and DOM extraction
- Multi-step loop with feedback and adaptation
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import traceback

# Core dependencies
from pydantic import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ActionType(str, Enum):
    """Supported automation actions"""
    NAVIGATE = "navigate"
    CLICK = "click"
    INPUT = "input"
    SELECT = "select"
    WAIT = "wait"
    EXTRACT = "extract"
    SCROLL = "scroll"
    SCREENSHOT = "screenshot"

class ExecutionStatus(str, Enum):
    """Execution status for steps and plans"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"

class RiskLevel(str, Enum):
    """Risk levels for automation plans"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# Data Models
class UserGoal(BaseModel):
    """User input for automation goal"""
    goal: str = Field(..., description="Natural language description of the goal")
    website_url: str = Field(..., description="Target website URL")
    context: Optional[str] = Field(None, description="Additional context about the task")
    credentials: Optional[Dict[str, str]] = Field(None, description="Login credentials if needed")
    max_steps: int = Field(20, description="Maximum number of steps to execute")
    timeout: int = Field(300, description="Maximum execution time in seconds")

class SelectorSet(BaseModel):
    """Set of selectors for an element with fallbacks"""
    primary: str = Field(..., description="Primary CSS/XPath selector")
    fallbacks: List[str] = Field(default_factory=list, description="Fallback selectors")
    confidence: float = Field(0.0, description="Confidence score (0-1)")
    element_description: str = Field("", description="Human-readable element description")

class PlanStep(BaseModel):
    """Individual step in the execution plan"""
    step_id: int = Field(..., description="Unique step identifier")
    action_type: ActionType = Field(..., description="Type of action to perform")
    description: str = Field(..., description="Human-readable step description")
    target_element: Optional[str] = Field(None, description="CSS/XPath selector for target element")
    target_url: Optional[str] = Field(None, description="URL for navigation actions")
    input_data: Optional[str] = Field(None, description="Data to input (for input actions)")
    wait_condition: Optional[str] = Field(None, description="Condition to wait for")
    success_criteria: str = Field(..., description="How to determine if step succeeded")
    selectors: Optional[SelectorSet] = Field(None, description="Determined selectors for this step")
    status: ExecutionStatus = Field(ExecutionStatus.PENDING, description="Current execution status")
    error_message: Optional[str] = Field(None, description="Error message if step failed")
    execution_time: Optional[float] = Field(None, description="Time taken to execute step")
    screenshot_path: Optional[str] = Field(None, description="Path to screenshot taken during step")

class ExecutionPlan(BaseModel):
    """Complete execution plan for a user goal"""
    plan_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique plan identifier")
    user_goal: UserGoal = Field(..., description="Original user goal")
    steps: List[PlanStep] = Field(..., description="Ordered list of execution steps")
    estimated_complexity: str = Field("medium", description="Estimated complexity (low/medium/high)")
    risk_level: RiskLevel = Field(RiskLevel.LOW, description="Risk level assessment")
    created_at: datetime = Field(default_factory=datetime.now, description="Plan creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    status: ExecutionStatus = Field(ExecutionStatus.PENDING, description="Overall plan status")

class DOMElement(BaseModel):
    """Representation of a DOM element"""
    tag: str = Field(..., description="HTML tag name")
    text: str = Field("", description="Element text content")
    attributes: Dict[str, str] = Field(default_factory=dict, description="Element attributes")
    xpath: str = Field("", description="XPath to element")
    css_selector: str = Field("", description="CSS selector to element")
    is_visible: bool = Field(True, description="Whether element is visible")
    is_clickable: bool = Field(False, description="Whether element is clickable")
    bounding_box: Optional[Dict[str, float]] = Field(None, description="Element bounding box")

class DOMState(BaseModel):
    """Current state of the DOM"""
    url: str = Field(..., description="Current page URL")
    title: str = Field("", description="Page title")
    elements: List[DOMElement] = Field(default_factory=list, description="Relevant DOM elements")
    screenshot_path: Optional[str] = Field(None, description="Path to page screenshot")
    timestamp: datetime = Field(default_factory=datetime.now, description="DOM capture timestamp")
    page_load_state: str = Field("", description="Page load state")

class ExecutionResult(BaseModel):
    """Result of executing a plan step"""
    step_id: int = Field(..., description="ID of executed step")
    status: ExecutionStatus = Field(..., description="Execution status")
    success: bool = Field(..., description="Whether step succeeded")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    dom_state_after: Optional[DOMState] = Field(None, description="DOM state after execution")
    execution_time: float = Field(..., description="Time taken to execute")
    screenshot_path: Optional[str] = Field(None, description="Screenshot after execution")
    extracted_data: Optional[Dict[str, Any]] = Field(None, description="Any data extracted during step")
    next_step_suggestions: Optional[List[str]] = Field(None, description="Suggestions for next steps")

class FinalResult(BaseModel):
    """Final result of the entire automation"""
    plan_id: str = Field(..., description="ID of executed plan")
    success: bool = Field(..., description="Whether goal was achieved")
    steps_executed: int = Field(..., description="Number of steps executed")
    total_time: float = Field(..., description="Total execution time")
    final_url: str = Field("", description="Final URL reached")
    extracted_data: Dict[str, Any] = Field(default_factory=dict, description="All extracted data")
    error_summary: Optional[str] = Field(None, description="Summary of any errors")
    screenshots: List[str] = Field(default_factory=list, description="Paths to all screenshots")
    execution_log: List[str] = Field(default_factory=list, description="Detailed execution log")


# Core Components
class GoalPlanner:
    """LLM-powered planner for goal decomposition and selector determination"""

    def __init__(self, model_name: str = "gemini-2.0-flash"):
        """Initialize the planner with LLM"""
        self.llm = ChatGoogleGenerativeAI(
            model=model_name,
            temperature=0.1,
            max_output_tokens=4096
        )
        self.planning_prompts = self._load_planning_prompts()

    def _load_planning_prompts(self) -> Dict[str, str]:
        """Load prompt templates for different planning tasks"""
        return {
            "goal_decomposition": """
You are an expert browser automation planner. Your task is to break down a user's goal into detailed, executable steps.

User Goal: {goal}
Website URL: {website_url}
Context: {context}

Break this down into a JSON plan with the following structure:
{
    "steps": [
        {
            "step_id": 1,
            "action_type": "navigate|click|input|select|wait|extract|scroll",
            "description": "Clear description of what this step does",
            "target_element": "CSS_SELECTOR_TO_BE_DETERMINED",
            "target_url": "URL if navigation step",
            "input_data": "Data to input if input step",
            "wait_condition": "What to wait for if wait step",
            "success_criteria": "How to know this step succeeded"
        }
    ],
    "estimated_complexity": "low|medium|high",
    "risk_level": "low|medium|high|critical"
}

Guidelines:
1. Be specific and detailed in step descriptions
2. Use "TBD_BY_EXECUTOR" for selectors that need DOM analysis
3. Include authentication steps if credentials are needed
4. Add wait steps after actions that change page state
5. Include validation steps to check success
6. Consider error scenarios and recovery steps
7. Keep steps atomic and focused

IMPORTANT: Return ONLY valid JSON in the exact format specified above. Do not include any markdown formatting, explanations, or additional text. The response must start with { and end with }.
""",

            "selector_determination": """
You are an expert at analyzing DOM structures to find the best CSS selectors for browser automation.

Current Step: {step_description}
Action Type: {action_type}
Target Description: {target_description}

DOM Elements (simplified):
{dom_elements}

Your task is to determine the best CSS selector for the target element. Return JSON:
{
    "primary": "most_reliable_css_selector",
    "fallbacks": ["fallback_selector_1", "fallback_selector_2"],
    "confidence": 0.95,
    "element_description": "Clear description of the target element"
}

Guidelines:
1. Prefer stable selectors (id, data attributes, semantic elements)
2. Avoid fragile selectors (nth-child, complex hierarchies)
3. Consider element visibility and interactability
4. Provide multiple fallback options
5. Confidence should reflect selector reliability (0-1)
6. Test selectors against the provided DOM structure

IMPORTANT: Return ONLY valid JSON in the exact format specified above. Do not include any markdown formatting, explanations, or additional text. The response must start with { and end with }.
""",

            "plan_adaptation": """
You are an expert at adapting automation plans based on execution feedback.

Original Plan Step: {original_step}
Execution Result: {execution_result}
Current DOM State: {dom_state}
Error Details: {error_details}

Analyze the situation and provide an updated plan. Return JSON:
{
    "action": "retry|skip|modify|insert_new_steps",
    "updated_step": {
        "step_id": 1,
        "action_type": "...",
        "description": "...",
        "target_element": "...",
        "success_criteria": "..."
    },
    "additional_steps": [],
    "reasoning": "Why this adaptation is needed"
}

Guidelines:
1. Analyze why the original step failed
2. Suggest concrete fixes (better selectors, different approach)
3. Consider inserting wait steps or alternative paths
4. Maintain the original goal while adapting the approach
5. Provide clear reasoning for changes

IMPORTANT: Return ONLY valid JSON in the exact format specified above. Do not include any markdown formatting, explanations, or additional text. The response must start with { and end with }.
"""
        }

    async def decompose_goal(self, user_goal: UserGoal) -> ExecutionPlan:
        """Break down user goal into executable steps"""
        try:
            logger.info(f"Decomposing goal: {user_goal.goal}")

            # Prepare prompt
            prompt_template = self.planning_prompts["goal_decomposition"]
            prompt = prompt_template.replace("{goal}", user_goal.goal)
            prompt = prompt.replace("{website_url}", user_goal.website_url)
            prompt = prompt.replace("{context}", user_goal.context or "General web automation")

            # Get LLM response
            response = await self.llm.ainvoke(prompt)

            # Debug: Log the raw response
            logger.info(f"Raw LLM response: {response.content}")

            # Parse JSON response
            try:
                plan_data = json.loads(response.content)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                logger.error(f"Raw response content: {response.content}")

                # Try to extract JSON from the response if it's wrapped in markdown
                content = response.content.strip()
                if "```json" in content:
                    # Extract JSON from markdown code block
                    start = content.find("```json") + 7
                    end = content.find("```", start)
                    if end != -1:
                        json_content = content[start:end].strip()
                        logger.info(f"Extracted JSON from markdown: {json_content}")
                        plan_data = json.loads(json_content)
                    else:
                        raise e
                elif content.startswith("{") and content.endswith("}"):
                    # Try parsing as-is
                    plan_data = json.loads(content)
                else:
                    # Create a fallback plan
                    logger.warning("Creating fallback plan due to JSON parsing error")
                    plan_data = {
                        "steps": [
                            {
                                "step_id": 1,
                                "action_type": "navigate",
                                "description": "Navigate to the target website",
                                "target_url": user_goal.website_url,
                                "success_criteria": "Page loads successfully"
                            },
                            {
                                "step_id": 2,
                                "action_type": "wait",
                                "description": "Wait for page to load",
                                "wait_condition": "2 seconds",
                                "success_criteria": "Page is ready"
                            },
                            {
                                "step_id": 3,
                                "action_type": "screenshot",
                                "description": "Take screenshot of the page",
                                "success_criteria": "Screenshot captured"
                            }
                        ],
                        "estimated_complexity": "medium",
                        "risk_level": "low"
                    }

            # Create plan steps
            steps = []
            for step_data in plan_data["steps"]:
                step = PlanStep(**step_data)
                steps.append(step)

            # Create execution plan
            plan = ExecutionPlan(
                user_goal=user_goal,
                steps=steps,
                estimated_complexity=plan_data.get("estimated_complexity", "medium"),
                risk_level=RiskLevel(plan_data.get("risk_level", "low"))
            )

            logger.info(f"Created plan with {len(steps)} steps")
            return plan

        except Exception as e:
            logger.error(f"Error decomposing goal: {e}")
            logger.error(traceback.format_exc())
            raise

    async def determine_selectors(self, dom_state: DOMState, step: PlanStep) -> SelectorSet:
        """Determine CSS selectors for a step based on current DOM"""
        try:
            logger.info(f"Determining selectors for step {step.step_id}: {step.description}")

            # Simplify DOM elements for LLM
            dom_elements = []
            for element in dom_state.elements[:50]:  # Limit to first 50 elements
                dom_elements.append({
                    "tag": element.tag,
                    "text": element.text[:100],  # Truncate long text
                    "attributes": element.attributes,
                    "css_selector": element.css_selector,
                    "is_visible": element.is_visible,
                    "is_clickable": element.is_clickable
                })

            # Prepare prompt
            prompt_template = self.planning_prompts["selector_determination"]
            prompt = prompt_template.replace("{step_description}", step.description)
            prompt = prompt.replace("{action_type}", step.action_type.value)
            prompt = prompt.replace("{target_description}", step.target_element or "Element to be determined")
            prompt = prompt.replace("{dom_elements}", json.dumps(dom_elements, indent=2))

            # Get LLM response
            response = await self.llm.ainvoke(prompt)

            # Parse JSON response
            selector_data = json.loads(response.content)

            # Create selector set
            selectors = SelectorSet(**selector_data)

            logger.info(f"Determined selector: {selectors.primary} (confidence: {selectors.confidence})")
            return selectors

        except Exception as e:
            logger.error(f"Error determining selectors: {e}")
            logger.error(traceback.format_exc())
            # Return fallback selector
            return SelectorSet(
                primary="body",
                fallbacks=["html"],
                confidence=0.1,
                element_description="Fallback selector due to error"
            )

    async def adapt_plan(self, original_step: PlanStep, execution_result: ExecutionResult,
                        dom_state: DOMState) -> Dict[str, Any]:
        """Adapt plan based on execution feedback"""
        try:
            logger.info(f"Adapting plan for failed step {original_step.step_id}")

            # Prepare prompt
            prompt_template = self.planning_prompts["plan_adaptation"]
            prompt = prompt_template.replace("{original_step}", original_step.model_dump_json())
            prompt = prompt.replace("{execution_result}", execution_result.model_dump_json())
            prompt = prompt.replace("{dom_state}", dom_state.model_dump_json() if dom_state else "{}")
            prompt = prompt.replace("{error_details}", execution_result.error_message or "Unknown error")

            # Get LLM response
            response = await self.llm.ainvoke(prompt)

            # Parse JSON response
            adaptation = json.loads(response.content)

            logger.info(f"Plan adaptation: {adaptation['action']} - {adaptation['reasoning']}")
            return adaptation

        except Exception as e:
            logger.error(f"Error adapting plan: {e}")
            logger.error(traceback.format_exc())
            # Return retry as fallback
            return {
                "action": "retry",
                "updated_step": original_step.model_dump(),
                "additional_steps": [],
                "reasoning": "Fallback retry due to adaptation error"
            }


class BrowserExecutor:
    """Browser automation executor with DOM extraction capabilities"""

    def __init__(self, headless: bool = False, timeout: int = 30000):
        """Initialize the browser executor"""
        self.headless = headless
        self.timeout = timeout
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.screenshots_dir = "screenshots"
        self._ensure_screenshots_dir()

    def _ensure_screenshots_dir(self):
        """Ensure screenshots directory exists"""
        os.makedirs(self.screenshots_dir, exist_ok=True)

    async def start_browser(self):
        """Start the browser and create context"""
        try:
            logger.info("Starting browser...")
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            self.page = await self.context.new_page()
            self.page.set_default_timeout(self.timeout)
            logger.info("Browser started successfully")

        except Exception as e:
            logger.error(f"Error starting browser: {e}")
            raise

    async def close_browser(self):
        """Close the browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            logger.info("Browser closed successfully")

        except Exception as e:
            logger.error(f"Error closing browser: {e}")

    async def extract_dom(self) -> DOMState:
        """Extract current DOM state for analysis"""
        try:
            if not self.page:
                raise Exception("Browser not started")

            logger.info("Extracting DOM state...")

            # Get basic page info
            url = self.page.url
            title = await self.page.title()

            # Take screenshot
            screenshot_path = f"{self.screenshots_dir}/dom_state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.page.screenshot(path=screenshot_path, full_page=True)

            # Extract interactive elements
            elements = await self.page.evaluate("""
                () => {
                    const elements = [];
                    const selectors = [
                        'input', 'button', 'a', 'select', 'textarea',
                        '[onclick]', '[role="button"]', '[tabindex]',
                        '.btn', '.button', '.link', '.clickable'
                    ];

                    selectors.forEach(selector => {
                        document.querySelectorAll(selector).forEach((el, index) => {
                            const rect = el.getBoundingClientRect();
                            const isVisible = rect.width > 0 && rect.height > 0 &&
                                            window.getComputedStyle(el).visibility !== 'hidden' &&
                                            window.getComputedStyle(el).display !== 'none';

                            if (isVisible) {
                                const attributes = {};
                                for (let attr of el.attributes) {
                                    attributes[attr.name] = attr.value;
                                }

                                elements.push({
                                    tag: el.tagName.toLowerCase(),
                                    text: el.textContent?.trim().substring(0, 200) || '',
                                    attributes: attributes,
                                    xpath: getXPath(el),
                                    css_selector: getCSSSelector(el),
                                    is_visible: isVisible,
                                    is_clickable: el.tagName.toLowerCase() === 'button' ||
                                                el.tagName.toLowerCase() === 'a' ||
                                                el.onclick !== null ||
                                                el.getAttribute('role') === 'button',
                                    bounding_box: {
                                        x: rect.x,
                                        y: rect.y,
                                        width: rect.width,
                                        height: rect.height
                                    }
                                });
                            }
                        });
                    });

                    function getXPath(element) {
                        if (element.id) return `//*[@id="${element.id}"]`;
                        if (element === document.body) return '/html/body';

                        let ix = 0;
                        const siblings = element.parentNode?.childNodes || [];
                        for (let i = 0; i < siblings.length; i++) {
                            const sibling = siblings[i];
                            if (sibling === element) {
                                return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                            }
                            if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                                ix++;
                            }
                        }
                        return '';
                    }

                    function getCSSSelector(element) {
                        if (element.id) return `#${element.id}`;
                        if (element.className) {
                            const classes = element.className.split(' ').filter(c => c.trim());
                            if (classes.length > 0) {
                                return element.tagName.toLowerCase() + '.' + classes.join('.');
                            }
                        }
                        return element.tagName.toLowerCase();
                    }

                    return elements.slice(0, 100); // Limit to first 100 elements
                }
            """)

            # Convert to DOMElement objects
            dom_elements = []
            for elem_data in elements:
                dom_element = DOMElement(**elem_data)
                dom_elements.append(dom_element)

            # Get page load state
            load_state = await self.page.evaluate("() => document.readyState")

            dom_state = DOMState(
                url=url,
                title=title,
                elements=dom_elements,
                screenshot_path=screenshot_path,
                page_load_state=load_state
            )

            logger.info(f"Extracted DOM with {len(dom_elements)} elements")
            return dom_state

        except Exception as e:
            logger.error(f"Error extracting DOM: {e}")
            logger.error(traceback.format_exc())
            # Return minimal DOM state
            return DOMState(
                url=self.page.url if self.page else "",
                title="Error extracting DOM",
                elements=[]
            )

    async def execute_step(self, step: PlanStep) -> ExecutionResult:
        """Execute a single automation step"""
        start_time = datetime.now()

        try:
            if not self.page:
                raise Exception("Browser not started")

            logger.info(f"Executing step {step.step_id}: {step.description}")

            # Take screenshot before execution
            screenshot_before = f"{self.screenshots_dir}/step_{step.step_id}_before.png"
            await self.page.screenshot(path=screenshot_before)

            success = False
            error_message = None
            extracted_data = {}

            # Execute based on action type
            if step.action_type == ActionType.NAVIGATE:
                success = await self._execute_navigate(step)

            elif step.action_type == ActionType.CLICK:
                success = await self._execute_click(step)

            elif step.action_type == ActionType.INPUT:
                success = await self._execute_input(step)

            elif step.action_type == ActionType.SELECT:
                success = await self._execute_select(step)

            elif step.action_type == ActionType.WAIT:
                success = await self._execute_wait(step)

            elif step.action_type == ActionType.EXTRACT:
                success, extracted_data = await self._execute_extract(step)

            elif step.action_type == ActionType.SCROLL:
                success = await self._execute_scroll(step)

            elif step.action_type == ActionType.SCREENSHOT:
                success = await self._execute_screenshot(step)

            else:
                raise Exception(f"Unsupported action type: {step.action_type}")

            # Take screenshot after execution
            screenshot_after = f"{self.screenshots_dir}/step_{step.step_id}_after.png"
            await self.page.screenshot(path=screenshot_after)

            # Get DOM state after execution
            dom_state_after = await self.extract_dom()

            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()

            # Validate success criteria if provided
            if success and step.success_criteria:
                success = await self._validate_success_criteria(step.success_criteria)

            result = ExecutionResult(
                step_id=step.step_id,
                status=ExecutionStatus.SUCCESS if success else ExecutionStatus.FAILED,
                success=success,
                error_message=error_message,
                dom_state_after=dom_state_after,
                execution_time=execution_time,
                screenshot_path=screenshot_after,
                extracted_data=extracted_data
            )

            logger.info(f"Step {step.step_id} {'succeeded' if success else 'failed'}")
            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_message = str(e)
            logger.error(f"Error executing step {step.step_id}: {error_message}")
            logger.error(traceback.format_exc())

            return ExecutionResult(
                step_id=step.step_id,
                status=ExecutionStatus.FAILED,
                success=False,
                error_message=error_message,
                execution_time=execution_time,
                screenshot_path=f"{self.screenshots_dir}/step_{step.step_id}_error.png"
            )

    async def _execute_navigate(self, step: PlanStep) -> bool:
        """Execute navigation action"""
        try:
            if not step.target_url:
                raise Exception("No target URL provided for navigation")

            await self.page.goto(step.target_url, wait_until='domcontentloaded')
            await asyncio.sleep(2)  # Wait for page to stabilize
            return True

        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return False

    async def _execute_click(self, step: PlanStep) -> bool:
        """Execute click action"""
        try:
            if not step.target_element:
                raise Exception("No target element provided for click")

            # Try primary selector first, then fallbacks
            selectors_to_try = [step.target_element]
            if step.selectors:
                selectors_to_try = [step.selectors.primary] + step.selectors.fallbacks

            for selector in selectors_to_try:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.click()
                        await asyncio.sleep(1)  # Wait for click to process
                        return True
                except:
                    continue

            raise Exception(f"Could not find clickable element with any selector")

        except Exception as e:
            logger.error(f"Click failed: {e}")
            return False

    async def _execute_input(self, step: PlanStep) -> bool:
        """Execute input action"""
        try:
            if not step.target_element or not step.input_data:
                raise Exception("Missing target element or input data")

            # Try primary selector first, then fallbacks
            selectors_to_try = [step.target_element]
            if step.selectors:
                selectors_to_try = [step.selectors.primary] + step.selectors.fallbacks

            for selector in selectors_to_try:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.clear()
                        await element.fill(step.input_data)
                        await asyncio.sleep(0.5)  # Wait for input to process
                        return True
                except:
                    continue

            raise Exception(f"Could not find input element with any selector")

        except Exception as e:
            logger.error(f"Input failed: {e}")
            return False

    async def _execute_select(self, step: PlanStep) -> bool:
        """Execute select action"""
        try:
            if not step.target_element or not step.input_data:
                raise Exception("Missing target element or selection value")

            element = await self.page.wait_for_selector(step.target_element, timeout=5000)
            if element:
                await element.select_option(value=step.input_data)
                await asyncio.sleep(0.5)
                return True
            return False

        except Exception as e:
            logger.error(f"Select failed: {e}")
            return False

    async def _execute_wait(self, step: PlanStep) -> bool:
        """Execute wait action"""
        try:
            if step.wait_condition:
                # Wait for specific condition
                if "selector:" in step.wait_condition:
                    selector = step.wait_condition.replace("selector:", "")
                    await self.page.wait_for_selector(selector, timeout=10000)
                elif "url:" in step.wait_condition:
                    url_pattern = step.wait_condition.replace("url:", "")
                    await self.page.wait_for_url(url_pattern, timeout=10000)
                else:
                    # Default wait time
                    await asyncio.sleep(3)
            else:
                # Default wait
                await asyncio.sleep(2)
            return True

        except Exception as e:
            logger.error(f"Wait failed: {e}")
            return False

    async def _execute_extract(self, step: PlanStep) -> tuple[bool, Dict[str, Any]]:
        """Execute data extraction"""
        try:
            extracted_data = {}

            if step.target_element:
                element = await self.page.wait_for_selector(step.target_element, timeout=5000)
                if element:
                    text = await element.text_content()
                    extracted_data["text"] = text

                    # Try to extract other attributes
                    attributes = await element.evaluate("el => Array.from(el.attributes).reduce((acc, attr) => {acc[attr.name] = attr.value; return acc;}, {})")
                    extracted_data["attributes"] = attributes

            return True, extracted_data

        except Exception as e:
            logger.error(f"Extract failed: {e}")
            return False, {}

    async def _execute_scroll(self, step: PlanStep) -> bool:
        """Execute scroll action"""
        try:
            if step.target_element:
                element = await self.page.wait_for_selector(step.target_element, timeout=5000)
                if element:
                    await element.scroll_into_view_if_needed()
            else:
                # Scroll page down
                await self.page.evaluate("window.scrollBy(0, window.innerHeight)")

            await asyncio.sleep(1)
            return True

        except Exception as e:
            logger.error(f"Scroll failed: {e}")
            return False

    async def _execute_screenshot(self, step: PlanStep) -> bool:
        """Execute screenshot action"""
        try:
            screenshot_path = f"{self.screenshots_dir}/manual_screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.page.screenshot(path=screenshot_path, full_page=True)
            return True

        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            return False

    async def _validate_success_criteria(self, criteria: str) -> bool:
        """Validate if success criteria is met"""
        try:
            if "selector_exists:" in criteria:
                selector = criteria.replace("selector_exists:", "")
                element = await self.page.query_selector(selector)
                return element is not None

            elif "url_contains:" in criteria:
                url_part = criteria.replace("url_contains:", "")
                return url_part in self.page.url

            elif "text_visible:" in criteria:
                text = criteria.replace("text_visible:", "")
                element = await self.page.query_selector(f"text={text}")
                return element is not None

            else:
                # Default validation - just check if page is loaded
                return await self.page.evaluate("document.readyState === 'complete'")

        except Exception as e:
            logger.error(f"Success criteria validation failed: {e}")
            return False


class SmartAutomationAgent:
    """Main orchestrator for the smart browser automation agent"""

    def __init__(self, headless: bool = False, model_name: str = "gemini-2.0-flash"):
        """Initialize the automation agent"""
        self.planner = GoalPlanner(model_name=model_name)
        self.executor = BrowserExecutor(headless=headless)
        self.execution_log = []

    async def execute_goal(self, user_goal: UserGoal) -> FinalResult:
        """Execute a user goal with multi-step planner-executor loop"""
        start_time = datetime.now()

        try:
            logger.info(f"Starting execution of goal: {user_goal.goal}")

            # Start browser
            await self.executor.start_browser()

            # Decompose goal into plan
            plan = await self.planner.decompose_goal(user_goal)

            # Execute plan with feedback loop
            result = await self._execute_plan_with_feedback(plan)

            # Calculate total execution time
            total_time = (datetime.now() - start_time).total_seconds()

            # Create final result
            final_result = FinalResult(
                plan_id=plan.plan_id,
                success=result["success"],
                steps_executed=result["steps_executed"],
                total_time=total_time,
                final_url=self.executor.page.url if self.executor.page else "",
                extracted_data=result["extracted_data"],
                error_summary=result.get("error_summary"),
                screenshots=result["screenshots"],
                execution_log=self.execution_log
            )

            logger.info(f"Goal execution completed. Success: {final_result.success}")
            return final_result

        except Exception as e:
            logger.error(f"Error executing goal: {e}")
            logger.error(traceback.format_exc())

            total_time = (datetime.now() - start_time).total_seconds()
            return FinalResult(
                plan_id="error",
                success=False,
                steps_executed=0,
                total_time=total_time,
                error_summary=str(e),
                screenshots=[],
                execution_log=self.execution_log
            )

        finally:
            # Always close browser
            await self.executor.close_browser()

    async def _execute_plan_with_feedback(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """Execute plan with feedback loop and adaptation"""
        steps_executed = 0
        extracted_data = {}
        screenshots = []
        errors = []

        try:
            for step_index, step in enumerate(plan.steps):
                if steps_executed >= plan.user_goal.max_steps:
                    logger.warning(f"Reached maximum steps limit: {plan.user_goal.max_steps}")
                    break

                self.execution_log.append(f"Starting step {step.step_id}: {step.description}")

                # Get current DOM state
                dom_state = await self.executor.extract_dom()

                # Determine selectors if needed
                if step.target_element == "TBD_BY_EXECUTOR" or not step.selectors:
                    selectors = await self.planner.determine_selectors(dom_state, step)
                    step.selectors = selectors
                    step.target_element = selectors.primary

                # Execute step
                execution_result = await self.executor.execute_step(step)
                steps_executed += 1

                # Add screenshot to collection
                if execution_result.screenshot_path:
                    screenshots.append(execution_result.screenshot_path)

                # Collect extracted data
                if execution_result.extracted_data:
                    extracted_data.update(execution_result.extracted_data)

                # Log execution result
                self.execution_log.append(
                    f"Step {step.step_id} {'succeeded' if execution_result.success else 'failed'}: "
                    f"{execution_result.error_message or 'OK'}"
                )

                # Handle failure with adaptation
                if not execution_result.success:
                    errors.append(f"Step {step.step_id}: {execution_result.error_message}")

                    # Try to adapt the plan
                    adaptation = await self.planner.adapt_plan(step, execution_result, dom_state)

                    if adaptation["action"] == "retry":
                        # Retry the same step with potential modifications
                        updated_step = PlanStep(**adaptation["updated_step"])
                        retry_result = await self.executor.execute_step(updated_step)

                        if retry_result.success:
                            self.execution_log.append(f"Step {step.step_id} retry succeeded")
                        else:
                            self.execution_log.append(f"Step {step.step_id} retry failed, continuing...")

                    elif adaptation["action"] == "skip":
                        self.execution_log.append(f"Skipping step {step.step_id} as suggested by planner")
                        continue

                    elif adaptation["action"] == "insert_new_steps":
                        # Insert new steps into the plan
                        new_steps = [PlanStep(**step_data) for step_data in adaptation["additional_steps"]]
                        plan.steps[step_index+1:step_index+1] = new_steps
                        self.execution_log.append(f"Inserted {len(new_steps)} new steps after step {step.step_id}")

                # Small delay between steps
                await asyncio.sleep(1)

            # Determine overall success
            success = len(errors) == 0 or len(errors) < len(plan.steps) * 0.5  # Success if less than 50% errors

            return {
                "success": success,
                "steps_executed": steps_executed,
                "extracted_data": extracted_data,
                "screenshots": screenshots,
                "error_summary": "; ".join(errors) if errors else None
            }

        except Exception as e:
            logger.error(f"Error in plan execution: {e}")
            return {
                "success": False,
                "steps_executed": steps_executed,
                "extracted_data": extracted_data,
                "screenshots": screenshots,
                "error_summary": str(e)
            }


# Example usage and main function
async def main():
    """Example usage of the Smart Browser Automation Agent"""

    # Example 1: Trading platform automation
    trading_goal = UserGoal(
        goal="Buy 100 shares of COALINDIA",
        website_url="https://kite.zerodha.com",
        context="Trading platform automation",
        credentials={
            "username": "your_username",
            "password": "your_password"
        },
        max_steps=15,
        timeout=300
    )

    # Example 2: E-commerce automation
    ecommerce_goal = UserGoal(
        goal="Search for iPhone 15 and add the first result to cart",
        website_url="https://amazon.com",
        context="E-commerce shopping automation",
        max_steps=10,
        timeout=180
    )

    # Example 3: Form filling automation
    form_goal = UserGoal(
        goal="Fill out the contact form with my details",
        website_url="https://example.com/contact",
        context="Form filling automation",
        max_steps=8,
        timeout=120
    )

    # Initialize agent
    agent = SmartAutomationAgent(
        headless=False,  # Set to True for headless execution
        model_name="gemini-2.0-flash"
    )

    try:
        # Execute the goal
        print("Starting Smart Browser Automation Agent...")
        print(f"Goal: {trading_goal.goal}")
        print(f"Website: {trading_goal.website_url}")

        result = await agent.execute_goal(trading_goal)

        # Print results
        print("\n" + "="*50)
        print("EXECUTION RESULTS")
        print("="*50)
        print(f"Success: {result.success}")
        print(f"Steps Executed: {result.steps_executed}")
        print(f"Total Time: {result.total_time:.2f} seconds")
        print(f"Final URL: {result.final_url}")

        if result.extracted_data:
            print(f"Extracted Data: {json.dumps(result.extracted_data, indent=2)}")

        if result.error_summary:
            print(f"Errors: {result.error_summary}")

        print(f"Screenshots: {len(result.screenshots)} captured")
        print(f"Execution Log: {len(result.execution_log)} entries")

        # Print execution log
        print("\nExecution Log:")
        for log_entry in result.execution_log:
            print(f"  - {log_entry}")

    except Exception as e:
        print(f"Error running agent: {e}")
        logger.error(traceback.format_exc())


def create_custom_goal() -> UserGoal:
    """Interactive function to create a custom goal"""
    print("\n" + "="*50)
    print("CREATE CUSTOM AUTOMATION GOAL")
    print("="*50)

    goal = input("Enter your automation goal: ")
    website_url = input("Enter the website URL: ")
    context = input("Enter context (optional): ") or None

    max_steps = input("Maximum steps (default 20): ")
    max_steps = int(max_steps) if max_steps.isdigit() else 20

    timeout = input("Timeout in seconds (default 300): ")
    timeout = int(timeout) if timeout.isdigit() else 300

    # Ask for credentials if needed
    need_credentials = input("Does this require login credentials? (y/n): ").lower() == 'y'
    credentials = None

    if need_credentials:
        username = input("Username: ")
        password = input("Password: ")
        credentials = {"username": username, "password": password}

    return UserGoal(
        goal=goal,
        website_url=website_url,
        context=context,
        credentials=credentials,
        max_steps=max_steps,
        timeout=timeout
    )


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        # Interactive mode
        goal = create_custom_goal()
        agent = SmartAutomationAgent(headless=False)

        async def run_interactive():
            result = await agent.execute_goal(goal)
            print(f"\nResult: {'Success' if result.success else 'Failed'}")

        asyncio.run(run_interactive())
    else:
        # Run example
        asyncio.run(main())
